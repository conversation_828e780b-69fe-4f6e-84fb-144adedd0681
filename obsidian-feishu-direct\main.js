/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => FeishuSharePlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian5 = require("obsidian");

// src/constants.ts
var FEISHU_CONFIG = {
  // API 基础地址
  BASE_URL: "https://open.feishu.cn/open-apis",
  // OAuth 相关地址
  AUTHORIZE_URL: "https://open.feishu.cn/open-apis/authen/v1/authorize",
  TOKEN_URL: "https://open.feishu.cn/open-apis/authen/v1/access_token",
  REFRESH_TOKEN_URL: "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",
  // API 权限范围
  SCOPES: "contact:user.base:readonly docx:document drive:drive drive:file:upload",
  // 文件上传相关
  UPLOAD_URL: "https://open.feishu.cn/open-apis/drive/v1/files/upload_all",
  // 文档创建相关
  DOC_CREATE_URL: "https://open.feishu.cn/open-apis/docx/v1/documents",
  // 文件夹相关
  FOLDER_LIST_URL: "https://open.feishu.cn/open-apis/drive/v1/files",
  // 用户信息
  USER_INFO_URL: "https://open.feishu.cn/open-apis/authen/v1/user_info"
};
var DEFAULT_SETTINGS = {
  appId: "",
  appSecret: "",
  callbackUrl: "https://md2feishu.xinqi.life/oauth-callback",
  accessToken: "",
  refreshToken: "",
  userInfo: null,
  defaultFolderId: "",
  defaultFolderName: "\u6211\u7684\u7A7A\u95F4"
};
var FEISHU_ERROR_MESSAGES = {
  1061002: "\u53C2\u6570\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u6587\u4EF6\u683C\u5F0F\u548C\u5927\u5C0F",
  1061005: "\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236",
  1061006: "\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301",
  99991663: "access_token \u65E0\u6548",
  99991664: "access_token \u5DF2\u8FC7\u671F",
  99991665: "refresh_token \u65E0\u6548",
  99991666: "refresh_token \u5DF2\u8FC7\u671F"
};

// src/feishu-api.ts
var import_obsidian = require("obsidian");
var scanLocalAssets = (content) => {
  const assetSet = /* @__PURE__ */ new Set();
  const assetRegex = /!\[\[([^\]]+)\]\]/g;
  let match;
  while ((match = assetRegex.exec(content)) !== null) {
    assetSet.add(match[1]);
  }
  return Array.from(assetSet);
};
var FeishuApiService = class {
  constructor(plugin) {
    this.plugin = plugin;
    this.settings = plugin.settings;
  }
  /**
   * 更新设置
   */
  updateSettings(settings) {
    this.settings = settings;
  }
  /**
   * 生成授权 URL
   */
  generateAuthUrl() {
    if (!this.settings.appId || !this.settings.appSecret) {
      throw new Error("\u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u914D\u7F6E\u98DE\u4E66\u5E94\u7528\u7684 App ID \u548C App Secret");
    }
    const state = this.generateRandomState();
    localStorage.setItem("feishu-oauth-state", state);
    const redirectUri = this.settings.callbackUrl;
    const params = new URLSearchParams({
      app_id: this.settings.appId,
      redirect_uri: redirectUri,
      scope: FEISHU_CONFIG.SCOPES,
      state,
      response_type: "code"
    });
    const authUrl = `${FEISHU_CONFIG.AUTHORIZE_URL}?${params.toString()}`;
    return authUrl;
  }
  /**
   * 处理授权回调（从协议处理器调用）
   */
  async processCallback(callbackUrl) {
    try {
      const url = new URL(callbackUrl);
      const code = url.searchParams.get("code");
      const state = url.searchParams.get("state");
      const error = url.searchParams.get("error");
      if (error) {
        console.error("OAuth error:", error);
        return false;
      }
      if (!code) {
        console.error("No authorization code in callback");
        return false;
      }
      const savedState = localStorage.getItem("feishu-oauth-state");
      if (savedState && state !== savedState) {
        console.error("State mismatch");
        return false;
      }
      return await this.handleOAuthCallback(code);
    } catch (error) {
      console.error("Process callback error:", error);
      return false;
    }
  }
  /**
   * 处理授权回调
   */
  async handleOAuthCallback(authCode) {
    try {
      if (!this.settings.appId || !this.settings.appSecret) {
        throw new Error("\u5E94\u7528\u914D\u7F6E\u4E0D\u5B8C\u6574");
      }
      const tokenResponse = await this.exchangeCodeForToken(authCode);
      if (!tokenResponse.success) {
        throw new Error(tokenResponse.error || "\u83B7\u53D6\u8BBF\u95EE\u4EE4\u724C\u5931\u8D25");
      }
      const userInfo = await this.getUserInfo();
      if (userInfo) {
        this.settings.userInfo = userInfo;
        new import_obsidian.Notice("\u2705 \u98DE\u4E66\u6388\u6743\u6210\u529F\uFF01");
        return true;
      } else {
        throw new Error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25");
      }
    } catch (error) {
      console.error("OAuth callback error:", error);
      new import_obsidian.Notice(`\u274C \u6388\u6743\u5931\u8D25: ${error.message}`);
      return false;
    }
  }
  /**
   * 使用授权码换取访问令牌
   */
  async exchangeCodeForToken(code) {
    try {
      const appTokenResponse = await (0, import_obsidian.requestUrl)({
        url: "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          app_id: this.settings.appId,
          app_secret: this.settings.appSecret
        })
      });
      const appTokenData = appTokenResponse.json || JSON.parse(appTokenResponse.text);
      if (appTokenData.code !== 0) {
        console.error("Failed to get app access token:", appTokenData);
        return { success: false, error: `\u83B7\u53D6\u5E94\u7528\u4EE4\u724C\u5931\u8D25: ${appTokenData.msg}` };
      }
      const appAccessToken = appTokenData.app_access_token;
      const requestBody = {
        grant_type: "authorization_code",
        code
      };
      const response = await (0, import_obsidian.requestUrl)({
        url: FEISHU_CONFIG.TOKEN_URL,
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${appAccessToken}`
        },
        body: JSON.stringify(requestBody)
      });
      let data;
      if (response.json && typeof response.json === "object") {
        data = response.json;
      } else if (response.text) {
        const responseText = response.text;
        data = JSON.parse(responseText);
      } else {
        console.log("Trying to call response.json()...");
        data = await response.json();
      }
      if (data.code === 0) {
        this.settings.accessToken = data.data.access_token;
        this.settings.refreshToken = data.data.refresh_token;
        return { success: true };
      } else {
        console.error("Token exchange failed:", data);
        return { success: false, error: data.msg };
      }
    } catch (error) {
      console.error("Token exchange error:", error);
      return { success: false, error: error.message };
    }
  }
  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      const response = await (0, import_obsidian.requestUrl)({
        url: FEISHU_CONFIG.USER_INFO_URL,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`,
          "Content-Type": "application/json"
        }
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        return {
          name: data.data.name,
          avatar_url: data.data.avatar_url,
          email: data.data.email,
          user_id: data.data.user_id
        };
      } else {
        console.error("Get user info failed:", data);
        return null;
      }
    } catch (error) {
      console.error("Get user info error:", error);
      return null;
    }
  }
  /**
   * 分享 Markdown 到飞书（完整流程：上传 → 转换 → 删除源文件）
   */
  async shareMarkdown(title, content, statusNotice) {
    try {
      if (statusNotice)
        statusNotice.setMessage("\u{1F50D} \u6B63\u5728\u68C0\u67E5\u6388\u6743\u72B6\u6001...");
      const tokenValid = await this.ensureValidTokenWithReauth(statusNotice);
      if (!tokenValid) {
        throw new Error("\u6388\u6743\u5931\u6548\u4E14\u91CD\u65B0\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u91CD\u65B0\u6388\u6743");
      }
      if (statusNotice)
        statusNotice.setMessage("\u{1F50D} \u6B63\u5728\u626B\u63CF\u672C\u5730\u8D44\u6E90...");
      const localAssets = scanLocalAssets(content);
      const assetMap = /* @__PURE__ */ new Map();
      const activeFile = this.plugin.app.workspace.getActiveFile();
      if (localAssets.length > 0 && activeFile) {
        if (statusNotice)
          statusNotice.setMessage(`\u{1F4E4} \u53D1\u73B0 ${localAssets.length} \u4E2A\u672C\u5730\u9644\u4EF6\uFF0C\u6B63\u5728\u4E0A\u4F20...`);
        for (let i = 0; i < localAssets.length; i++) {
          const assetName = localAssets[i];
          if (statusNotice)
            statusNotice.setMessage(`\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u9644\u4EF6 ${i + 1}/${localAssets.length}: ${assetName}`);
          const assetFile = this.plugin.app.metadataCache.getFirstLinkpathDest(assetName, activeFile.path);
          if (assetFile) {
            const fileContent = await this.plugin.app.vault.readBinary(assetFile);
            const asset = await this.uploadAsset(assetName, fileContent);
            if (statusNotice)
              statusNotice.setMessage(`\u{1F517} \u83B7\u53D6\u9644\u4EF6 ${i + 1}/${localAssets.length} \u4E34\u65F6\u4E0B\u8F7D\u94FE\u63A5...`);
            const tempDownloadUrl = await this.getTempDownloadUrl(asset.token);
            if (tempDownloadUrl) {
              assetMap.set(assetName, tempDownloadUrl);
              console.log(`Uploaded asset ${assetName} with token ${asset.token} and temp download URL: ${tempDownloadUrl}`);
            } else {
              console.warn(`Failed to get temp download URL for ${assetName}, using fallback URL`);
              assetMap.set(assetName, asset.url);
            }
          } else {
            console.warn(`Asset not found in vault: ${assetName}`);
          }
        }
      }
      let processedContent = content;
      if (assetMap.size > 0) {
        statusNotice == null ? void 0 : statusNotice.setMessage("\u{1F504} \u6B63\u5728\u66FF\u6362\u56FE\u7247\u94FE\u63A5...");
        assetMap.forEach((url, assetName) => {
          const escapedAssetName = assetName.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");
          const imageRegex = new RegExp(`![[${escapedAssetName}]]`, "g");
          processedContent = processedContent.replace(imageRegex, `![${assetName}](${url})`);
        });
      }
      if (statusNotice)
        statusNotice.setMessage("\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u5904\u7406\u540E\u7684Markdown\u7B14\u8BB0...");
      const uploadResult = await this.uploadMarkdownFile(title, processedContent);
      if (!uploadResult.success || !uploadResult.fileToken) {
        throw new Error(uploadResult.error || "\u4E0A\u4F20Markdown\u6587\u4EF6\u5931\u8D25");
      }
      if (statusNotice)
        statusNotice.setMessage("\u{1F504} \u6B63\u5728\u5C06\u7B14\u8BB0\u8F6C\u6362\u4E3A\u98DE\u4E66\u6587\u6863...");
      const importTaskResult = await this.createImportTaskWithCorrectFolder(uploadResult.fileToken, title);
      if (!importTaskResult.success || !importTaskResult.ticket) {
        throw new Error(importTaskResult.error || "\u521B\u5EFA\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25");
      }
      if (statusNotice)
        statusNotice.setMessage("\u23F3 \u7B49\u5F85\u98DE\u4E66\u5904\u7406\u6587\u6863...");
      const completionResult = await this.waitForImportCompletionWithTimeout(importTaskResult.ticket, 3e4);
      if (!completionResult.success || !completionResult.documentToken) {
        throw new Error(completionResult.error || "\u6587\u6863\u8F6C\u6362\u5931\u8D25\u6216\u8D85\u65F6");
      }
      if (statusNotice)
        statusNotice.setMessage("\u{1F9F9} \u6E05\u7406\u4E34\u65F6\u6587\u4EF6...");
      await this.deleteSourceFile(uploadResult.fileToken);
      const docUrl = `https://feishu.cn/docx/${completionResult.documentToken}`;
      if (statusNotice)
        statusNotice.setMessage("\u2705 \u5206\u4EAB\u6210\u529F\uFF01");
      return { success: true, title, url: docUrl };
    } catch (error) {
      console.error("Share markdown error:", error);
      if (statusNotice)
        statusNotice.hide();
      new import_obsidian.Notice(`\u274C \u5206\u4EAB\u5931\u8D25: ${error.message}`);
      return { success: false, error: error.message };
    }
  }
  async uploadAsset(fileName, fileContent) {
    const boundary = `----FeishuObsidianPlugin${Date.now().toString(16)}`;
    const body = this.createMultipartBody(fileName, fileContent, boundary);
    const response = await (0, import_obsidian.requestUrl)({
      url: FEISHU_CONFIG.UPLOAD_URL,
      method: "POST",
      headers: {
        "Authorization": `Bearer ${this.settings.accessToken}`,
        "Content-Type": `multipart/form-data; boundary=${boundary}`
      },
      body
    });
    const data = response.json;
    if (data.code !== 0) {
      throw new Error(`\u4E0A\u4F20\u8D44\u4EA7\u5931\u8D25: ${data.msg}`);
    }
    const fileToken = data.data.file_token;
    const fallbackUrl = `https://feishu.cn/file/${fileToken}`;
    return { token: fileToken, url: fallbackUrl };
  }
  /**
   * 获取文件的临时下载链接
   */
  async getTempDownloadUrl(fileToken) {
    try {
      const params = new URLSearchParams({
        file_tokens: fileToken
      });
      const response = await (0, import_obsidian.requestUrl)({
        url: `${FEISHU_CONFIG.BASE_URL}/drive/v1/medias/batch_get_tmp_download_url?${params.toString()}`,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`,
          "Content-Type": "application/json"
        }
      });
      const data = response.json;
      if (data.code === 0 && data.data && data.data.tmp_download_urls) {
        const downloadUrls = data.data.tmp_download_urls;
        if (Array.isArray(downloadUrls) && downloadUrls.length > 0) {
          const downloadInfo = downloadUrls.find((item) => item.file_token === fileToken);
          if (downloadInfo && downloadInfo.tmp_download_url) {
            console.log(`Successfully got temp download URL for ${fileToken}: ${downloadInfo.tmp_download_url}`);
            return downloadInfo.tmp_download_url;
          }
        }
      }
      console.error(`Failed to get temp download URL for ${fileToken}:`, data);
      return null;
    } catch (error) {
      console.error(`Error getting temp download URL for ${fileToken}:`, error);
      return null;
    }
  }
  /**
   * 获取文件夹列表
   */
  async getFolderList(parentFolderId) {
    try {
      const tokenValid = await this.ensureValidToken();
      if (!tokenValid) {
        throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");
      }
      const url = `${FEISHU_CONFIG.BASE_URL}/drive/v1/files`;
      const params = new URLSearchParams({
        folder_token: parentFolderId || "",
        page_size: "50"
      });
      const response = await (0, import_obsidian.requestUrl)({
        url: `${url}?${params.toString()}`,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`,
          "Content-Type": "application/json"
        }
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        const folders = data.data.files.filter((file) => file.type === "folder").map((file) => ({
          ...file,
          folder_token: file.token,
          // 添加兼容属性
          token: file.token
          // 保留原始属性
        }));
        return {
          code: 0,
          data: {
            folders,
            has_more: data.data.has_more
          }
        };
      } else {
        throw new Error(data.msg || "\u83B7\u53D6\u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25");
      }
    } catch (error) {
      console.error("Get folder list error:", error);
      throw error;
    }
  }
  /**
   * 上传 Markdown 文件到飞书
   */
  async uploadMarkdownFile(fileName, content) {
    try {
      const tokenValid = await this.ensureValidToken();
      if (!tokenValid) {
        throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");
      }
      const boundary = "---7MA4YWxkTrZu0gW";
      const finalFileName = fileName.endsWith(".md") ? fileName : `${fileName}.md`;
      const utf8Content = new TextEncoder().encode(content);
      const contentLength = utf8Content.length;
      const parts = [];
      parts.push(`--${boundary}`);
      parts.push(`Content-Disposition: form-data; name="file_name"`);
      parts.push("");
      parts.push(finalFileName);
      parts.push(`--${boundary}`);
      parts.push(`Content-Disposition: form-data; name="parent_type"`);
      parts.push("");
      parts.push("explorer");
      parts.push(`--${boundary}`);
      parts.push(`Content-Disposition: form-data; name="size"`);
      parts.push("");
      parts.push(contentLength.toString());
      if (this.settings.defaultFolderId && this.settings.defaultFolderId !== "" && this.settings.defaultFolderId !== "nodcn2EG5YG1i5Rsh5uZs0FsUje") {
        parts.push(`--${boundary}`);
        parts.push(`Content-Disposition: form-data; name="parent_node"`);
        parts.push("");
        parts.push(this.settings.defaultFolderId);
        console.log("\u{1F4C1} Upload: Using custom folder:", this.settings.defaultFolderId, "(" + this.settings.defaultFolderName + ")");
      } else {
        console.log("\u{1F4C1} Upload: Using root folder (\u6211\u7684\u7A7A\u95F4) - no parent_node specified");
      }
      parts.push(`--${boundary}`);
      parts.push(`Content-Disposition: form-data; name="file"; filename="${finalFileName}"`);
      parts.push(`Content-Type: text/markdown`);
      parts.push("");
      const textPart = parts.join("\r\n") + "\r\n";
      const endBoundary = `\r
--${boundary}--\r
`;
      const textPartBytes = new TextEncoder().encode(textPart);
      const endBoundaryBytes = new TextEncoder().encode(endBoundary);
      const totalLength = textPartBytes.length + utf8Content.length + endBoundaryBytes.length;
      const bodyBytes = new Uint8Array(totalLength);
      let offset = 0;
      bodyBytes.set(textPartBytes, offset);
      offset += textPartBytes.length;
      bodyBytes.set(utf8Content, offset);
      offset += utf8Content.length;
      bodyBytes.set(endBoundaryBytes, offset);
      const response = await (0, import_obsidian.requestUrl)({
        url: FEISHU_CONFIG.UPLOAD_URL,
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`,
          "Content-Type": `multipart/form-data; boundary=${boundary}`
        },
        body: bodyBytes.buffer
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        const fileUrl = `https://feishu.cn/file/${data.data.file_token}`;
        return {
          success: true,
          fileToken: data.data.file_token,
          url: fileUrl
        };
      } else {
        const errorMsg = FEISHU_ERROR_MESSAGES[data.code] || data.msg || "\u4E0A\u4F20\u5931\u8D25";
        console.error("Upload failed:", data);
        return {
          success: false,
          error: errorMsg
        };
      }
    } catch (error) {
      console.error("Upload file error:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  /**
   * 刷新访问令牌
   */
  async refreshAccessToken() {
    try {
      if (!this.settings.refreshToken) {
        return false;
      }
      const response = await (0, import_obsidian.requestUrl)({
        url: FEISHU_CONFIG.REFRESH_TOKEN_URL,
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          grant_type: "refresh_token",
          refresh_token: this.settings.refreshToken
        })
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        this.settings.accessToken = data.data.access_token;
        this.settings.refreshToken = data.data.refresh_token;
        return true;
      } else {
        console.error("Token refresh failed:", data);
        return false;
      }
    } catch (error) {
      console.error("Token refresh error:", error);
      return false;
    }
  }
  /**
   * 生成随机状态值
   */
  generateRandomState() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
  /**
   * 检查并刷新token
   */
  async ensureValidToken() {
    if (!this.settings.accessToken) {
      return false;
    }
    try {
      const response = await (0, import_obsidian.requestUrl)({
        url: FEISHU_CONFIG.USER_INFO_URL,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`
        }
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        return true;
      } else if (data.code === 99991664) {
        return await this.refreshAccessToken();
      } else {
        return false;
      }
    } catch (error) {
      console.error("Token validation error:", error);
      return false;
    }
  }
  /**
   * 增强的token验证，支持自动重新授权
   */
  async ensureValidTokenWithReauth(statusNotice) {
    if (!this.settings.accessToken) {
      return await this.triggerReauth("\u6CA1\u6709\u8BBF\u95EE\u4EE4\u724C", statusNotice);
    }
    try {
      const response = await (0, import_obsidian.requestUrl)({
        url: FEISHU_CONFIG.USER_INFO_URL,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`
        }
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        return true;
      } else if (this.isTokenExpiredError(data.code)) {
        const refreshSuccess = await this.refreshAccessToken();
        if (refreshSuccess) {
          return true;
        } else {
          const reauthSuccess = await this.triggerReauth("Token\u5237\u65B0\u5931\u8D25", statusNotice);
          if (reauthSuccess) {
            return true;
          }
          return false;
        }
      } else {
        const reauthSuccess = await this.triggerReauth(`Token\u65E0\u6548 (\u9519\u8BEF\u7801: ${data.code})`, statusNotice);
        if (reauthSuccess) {
          return true;
        }
        return false;
      }
    } catch (error) {
      console.error("Token\u9A8C\u8BC1\u51FA\u9519:", error);
      const reauthSuccess = await this.triggerReauth("Token\u9A8C\u8BC1\u51FA\u9519", statusNotice);
      if (reauthSuccess) {
        return true;
      }
      return false;
    }
  }
  /**
   * 判断是否为token过期相关的错误码
   */
  isTokenExpiredError(code) {
    const expiredCodes = [
      99991664,
      // access_token expired
      99991663,
      // access_token invalid
      99991665,
      // refresh_token expired
      99991666,
      // refresh_token invalid
      1
      // 通用的无效token错误
    ];
    return expiredCodes.includes(code);
  }
  /**
   * 触发重新授权流程，支持等待授权完成
   */
  async triggerReauth(reason, statusNotice) {
    if (statusNotice) {
      statusNotice.setMessage(`\u{1F504} ${reason}\uFF0C\u6B63\u5728\u81EA\u52A8\u91CD\u65B0\u6388\u6743...`);
    } else {
      new import_obsidian.Notice(`\u{1F504} ${reason}\uFF0C\u6B63\u5728\u81EA\u52A8\u91CD\u65B0\u6388\u6743...`);
    }
    try {
      if (!this.settings.appId || !this.settings.appSecret) {
        const errorMsg = "\u274C \u5E94\u7528\u914D\u7F6E\u4E0D\u5B8C\u6574\uFF0C\u8BF7\u5728\u8BBE\u7F6E\u4E2D\u914D\u7F6E App ID \u548C App Secret";
        if (statusNotice) {
          statusNotice.setMessage(errorMsg);
          setTimeout(() => statusNotice.hide(), 3e3);
        } else {
          new import_obsidian.Notice(errorMsg);
        }
        return false;
      }
      const authUrl = this.generateAuthUrl();
      window.open(authUrl, "_blank");
      if (statusNotice) {
        statusNotice.setMessage("\u{1F310} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u91CD\u65B0\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u7EE7\u7EED\u5206\u4EAB...");
      } else {
        new import_obsidian.Notice("\u{1F310} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u91CD\u65B0\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u7EE7\u7EED\u5206\u4EAB...");
      }
      return await this.waitForReauth(statusNotice);
    } catch (error) {
      console.error("\u91CD\u65B0\u6388\u6743\u5931\u8D25:", error);
      new import_obsidian.Notice(`\u274C \u91CD\u65B0\u6388\u6743\u5931\u8D25: ${error.message}`);
      return false;
    }
  }
  /**
   * 等待重新授权完成
   */
  async waitForReauth(statusNotice) {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        window.removeEventListener("feishu-auth-success", successHandler);
        const timeoutMsg = "\u23F0 \u6388\u6743\u7B49\u5F85\u8D85\u65F6\uFF0C\u8BF7\u624B\u52A8\u91CD\u8BD5\u5206\u4EAB";
        if (statusNotice) {
          statusNotice.setMessage(timeoutMsg);
          setTimeout(() => statusNotice.hide(), 3e3);
        } else {
          new import_obsidian.Notice(timeoutMsg);
        }
        resolve(false);
      }, 5 * 60 * 1e3);
      const successHandler = () => {
        clearTimeout(timeout);
        window.removeEventListener("feishu-auth-success", successHandler);
        if (statusNotice) {
          statusNotice.setMessage("\u2705 \u6388\u6743\u6210\u529F\uFF0C\u6B63\u5728\u7EE7\u7EED\u5206\u4EAB...");
        }
        setTimeout(() => {
          resolve(true);
        }, 1e3);
      };
      window.addEventListener("feishu-auth-success", successHandler);
    });
  }
  /**
   * 创建导入任务（带正确的文件夹设置）
   */
  async createImportTaskWithCorrectFolder(fileToken, title) {
    try {
      const importData = {
        file_extension: "md",
        file_token: fileToken,
        type: "docx",
        file_name: title,
        point: {
          mount_type: 1,
          // 1=云空间
          mount_key: this.settings.defaultFolderId || "nodcn2EG5YG1i5Rsh5uZs0FsUje"
          // 使用设置的文件夹或默认根文件夹
        }
      };
      if (this.settings.defaultFolderId && this.settings.defaultFolderId !== "" && this.settings.defaultFolderId !== "nodcn2EG5YG1i5Rsh5uZs0FsUje") {
        console.log("\u2705 Import: Using custom folder:", this.settings.defaultFolderId, "(" + this.settings.defaultFolderName + ")");
      } else {
        console.log("\u2705 Import: Using default root folder (\u6211\u7684\u7A7A\u95F4)");
      }
      console.log("Import task request:", JSON.stringify(importData, null, 2));
      const response = await (0, import_obsidian.requestUrl)({
        url: `${FEISHU_CONFIG.BASE_URL}/drive/v1/import_tasks`,
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(importData)
      });
      const data = response.json || JSON.parse(response.text);
      console.log("Import task response:", JSON.stringify(data, null, 2));
      if (data.code === 0) {
        return {
          success: true,
          ticket: data.data.ticket
        };
      } else {
        return {
          success: false,
          error: data.msg || "\u521B\u5EFA\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25"
        };
      }
    } catch (error) {
      console.error("Create import task error:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  /**
   * 等待导入完成（带超时）
   */
  async waitForImportCompletionWithTimeout(ticket, timeoutMs) {
    const startTime = Date.now();
    const maxAttempts = 25;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime >= timeoutMs) {
        console.warn(`Import timeout after ${elapsedTime}ms`);
        return {
          success: false,
          error: `\u5BFC\u5165\u4EFB\u52A1\u8D85\u65F6 (${timeoutMs}ms)`
        };
      }
      try {
        const result = await this.checkImportStatus(ticket);
        if (result.success && (result.status === 3 || result.status === 0)) {
          if (result.documentToken) {
            const totalTime = Date.now() - startTime;
            return {
              success: true,
              documentToken: result.documentToken
            };
          } else {
            console.warn("Import completed but no document token returned, continuing to wait...");
          }
        } else if (result.success && result.status === 2) {
          console.warn(`Import shows failure status (${result.status}), but continuing to wait...`);
          if (attempt <= 8) {
          } else {
            console.error("Import failed after extended waiting");
            return {
              success: false,
              error: "\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25"
            };
          }
        } else {
        }
        if (attempt < maxAttempts) {
          const delay = this.getDelayForAttempt(attempt);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      } catch (error) {
        console.error("Check import status error:", error);
        const delay = this.getDelayForAttempt(attempt);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    return {
      success: false,
      error: "\u5BFC\u5165\u4EFB\u52A1\u8D85\u65F6"
    };
  }
  /**
   * 获取渐进式延迟时间
   */
  getDelayForAttempt(attempt) {
    if (attempt <= 3) {
      return 1e3;
    } else if (attempt <= 8) {
      return 2e3;
    } else {
      return 3e3;
    }
  }
  /**
   * 检查导入状态
   */
  async checkImportStatus(ticket) {
    try {
      const response = await (0, import_obsidian.requestUrl)({
        url: `${FEISHU_CONFIG.BASE_URL}/drive/v1/import_tasks/${ticket}`,
        method: "GET",
        headers: {
          "Authorization": `Bearer ${this.settings.accessToken}`,
          "Content-Type": "application/json"
        }
      });
      const data = response.json || JSON.parse(response.text);
      if (data.code === 0) {
        const result = data.data.result;
        return {
          success: true,
          status: result.job_status,
          documentToken: result.token
        };
      } else {
        return {
          success: false,
          error: data.msg || "\u68C0\u67E5\u5BFC\u5165\u72B6\u6001\u5931\u8D25"
        };
      }
    } catch (error) {
      console.error("Check import status error:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  /**
   * 删除源文件
   */
  async deleteSourceFile(fileToken) {
    try {
      let response;
      try {
        response = await (0, import_obsidian.requestUrl)({
          url: `${FEISHU_CONFIG.BASE_URL}/drive/v1/files/${fileToken}/trash`,
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.settings.accessToken}`,
            "Content-Type": "application/json"
          },
          body: JSON.stringify({})
        });
      } catch (trashError) {
        console.warn("\u26A0\uFE0F Trash method failed, trying direct delete...");
        response = await (0, import_obsidian.requestUrl)({
          url: `${FEISHU_CONFIG.BASE_URL}/drive/v1/files/${fileToken}?type=file`,
          method: "DELETE",
          headers: {
            "Authorization": `Bearer ${this.settings.accessToken}`,
            "Content-Type": "application/json"
          }
        });
      }
      if (response.status !== 200) {
        throw new Error(`\u5220\u9664\u8BF7\u6C42\u5931\u8D25\uFF0C\u72B6\u6001\u7801: ${response.status}`);
      }
      const data = response.json || JSON.parse(response.text);
      if (data.code !== 0) {
        console.warn("\u26A0\uFE0F Delete API returned non-zero code:", data.code, data.msg);
        console.log("\u{1F4DD} Source file deletion completed (may have been moved to trash)");
      } else {
      }
    } catch (error) {
      console.error("\u274C Delete source file error:", error);
    }
  }
  createMultipartBody(fileName, fileContent, boundary) {
    const pre_parts = [
      `--${boundary}`,
      `Content-Disposition: form-data; name="file_name"`,
      "",
      fileName,
      `--${boundary}`,
      `Content-Disposition: form-data; name="parent_type"`,
      "",
      "explorer",
      `--${boundary}`,
      `Content-Disposition: form-data; name="parent_node"`,
      "",
      this.settings.defaultFolderId || "",
      `--${boundary}`,
      `Content-Disposition: form-data; name="size"`,
      "",
      fileContent.byteLength.toString(),
      `--${boundary}`,
      `Content-Disposition: form-data; name="file"; filename="${fileName}"`,
      "Content-Type: application/octet-stream",
      ""
    ];
    const pre = new TextEncoder().encode(pre_parts.join("\r\n") + "\r\n");
    const suf = new TextEncoder().encode("\r\n--" + boundary + "--\r\n");
    const body = new ArrayBuffer(pre.byteLength + fileContent.byteLength + suf.byteLength);
    const view = new Uint8Array(body);
    view.set(new Uint8Array(pre), 0);
    view.set(new Uint8Array(fileContent), pre.byteLength);
    view.set(new Uint8Array(suf), pre.byteLength + fileContent.byteLength);
    return body;
  }
};

// src/settings.ts
var import_obsidian4 = require("obsidian");

// src/manual-auth-modal.ts
var import_obsidian2 = require("obsidian");
var ManualAuthModal = class extends import_obsidian2.Modal {
  constructor(app, feishuApi, onSuccess) {
    super(app);
    this.feishuApi = feishuApi;
    this.onSuccess = onSuccess;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    contentEl.createEl("h2", { text: "\u{1F510} \u98DE\u4E66\u624B\u52A8\u6388\u6743" });
    const descEl = contentEl.createDiv("setting-item-description");
    descEl.style.marginBottom = "20px";
    descEl.innerHTML = `
			<p><strong>\u{1F680} \u7B80\u5316\u6388\u6743\u6D41\u7A0B - \u53EA\u9700\u590D\u5236\u7C98\u8D34URL\uFF1A</strong></p>
			<ol>
				<li>\u70B9\u51FB\u4E0B\u65B9\u7684"\u6253\u5F00\u6388\u6743\u9875\u9762"\u6309\u94AE</li>
				<li>\u5728\u5F39\u51FA\u7684\u98DE\u4E66\u9875\u9762\u4E2D\u767B\u5F55\u5E76\u786E\u8BA4\u6388\u6743</li>
				<li>\u6388\u6743\u6210\u529F\u540E\uFF0C\u4F1A\u8DF3\u8F6C\u5230\u4E00\u4E2A\u663E\u793A\u9519\u8BEF\u7684\u9875\u9762\uFF08\u8FD9\u662F\u6B63\u5E38\u7684\uFF09</li>
				<li><strong>\u590D\u5236\u6D4F\u89C8\u5668\u5730\u5740\u680F\u7684\u5B8C\u6574URL</strong>\uFF08\u5305\u542B code= \u53C2\u6570\uFF09</li>
				<li>\u5C06\u5B8C\u6574URL\u7C98\u8D34\u5230\u4E0B\u65B9\u8F93\u5165\u6846\u4E2D</li>
				<li>\u70B9\u51FB"\u5B8C\u6210\u6388\u6743"\u6309\u94AE</li>
			</ol>
			<div style="background: var(--background-modifier-success); padding: 10px; border-radius: 4px; margin-top: 10px;">
				<strong>\u{1F4A1} \u63D0\u793A\uFF1A</strong>\u65E0\u9700\u624B\u52A8\u63D0\u53D6\u6388\u6743\u7801\uFF0C\u76F4\u63A5\u590D\u5236\u5B8C\u6574\u7684\u56DE\u8C03URL\u5373\u53EF\uFF01
			</div>
		`;
    new import_obsidian2.Setting(contentEl).setName("\u7B2C\u4E00\u6B65\uFF1A\u6253\u5F00\u6388\u6743\u9875\u9762").setDesc("\u70B9\u51FB\u6309\u94AE\u5728\u6D4F\u89C8\u5668\u4E2D\u6253\u5F00\u98DE\u4E66\u6388\u6743\u9875\u9762").addButton((button) => {
      button.setButtonText("\u{1F310} \u6253\u5F00\u6388\u6743\u9875\u9762").setCta().onClick(() => {
        try {
          const authUrl = this.feishuApi.generateAuthUrl();
          window.open(authUrl, "_blank");
          new import_obsidian2.Notice("\u2705 \u6388\u6743\u9875\u9762\u5DF2\u6253\u5F00\uFF0C\u8BF7\u5728\u6D4F\u89C8\u5668\u4E2D\u5B8C\u6210\u6388\u6743");
        } catch (error) {
          new import_obsidian2.Notice(`\u274C \u751F\u6210\u6388\u6743\u94FE\u63A5\u5931\u8D25: ${error.message}`);
        }
      });
    });
    let callbackUrl = "";
    new import_obsidian2.Setting(contentEl).setName("\u7B2C\u4E8C\u6B65\uFF1A\u7C98\u8D34\u56DE\u8C03URL").setDesc("\u4ECE\u6D4F\u89C8\u5668\u5730\u5740\u680F\u590D\u5236\u5B8C\u6574\u7684\u56DE\u8C03URL\u5E76\u7C98\u8D34\u5230\u6B64\u5904").addTextArea((text) => {
      text.setPlaceholder("\u7C98\u8D34\u5B8C\u6574\u7684\u56DE\u8C03URL\uFF0C\u4F8B\u5982\uFF1Ahttps://example.com/callback?code=xxx&state=xxx").setValue(callbackUrl).onChange((value) => {
        callbackUrl = value.trim();
      });
      text.inputEl.style.width = "100%";
      text.inputEl.style.height = "80px";
    });
    new import_obsidian2.Setting(contentEl).setName("\u7B2C\u4E09\u6B65\uFF1A\u5B8C\u6210\u6388\u6743").setDesc("\u89E3\u6790\u56DE\u8C03URL\u5E76\u5B8C\u6210\u6388\u6743\u6D41\u7A0B").addButton((button) => {
      button.setButtonText("\u2705 \u5B8C\u6210\u6388\u6743").setCta().onClick(async () => {
        await this.processCallback(callbackUrl);
      });
    });
    new import_obsidian2.Setting(contentEl).addButton((button) => {
      button.setButtonText("\u53D6\u6D88").onClick(() => {
        this.close();
      });
    });
  }
  async processCallback(callbackUrl) {
    try {
      if (!callbackUrl) {
        new import_obsidian2.Notice("\u274C \u8BF7\u5148\u7C98\u8D34\u56DE\u8C03URL");
        return;
      }
      const url = new URL(callbackUrl);
      const code = url.searchParams.get("code");
      const state = url.searchParams.get("state");
      if (!code) {
        new import_obsidian2.Notice("\u274C \u56DE\u8C03URL\u4E2D\u672A\u627E\u5230\u6388\u6743\u7801\uFF0C\u8BF7\u68C0\u67E5URL\u662F\u5426\u5B8C\u6574");
        return;
      }
      const savedState = localStorage.getItem("feishu-oauth-state");
      if (savedState && state !== savedState) {
        new import_obsidian2.Notice("\u274C \u72B6\u6001\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");
        return;
      }
      new import_obsidian2.Notice("\u{1F504} \u6B63\u5728\u5904\u7406\u6388\u6743...");
      const success = await this.feishuApi.handleOAuthCallback(code);
      if (success) {
        new import_obsidian2.Notice("\u{1F389} \u6388\u6743\u6210\u529F\uFF01");
        this.onSuccess();
        this.close();
      } else {
        new import_obsidian2.Notice("\u274C \u6388\u6743\u5904\u7406\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
      }
    } catch (error) {
      console.error("Process callback error:", error);
      new import_obsidian2.Notice(`\u274C \u5904\u7406\u6388\u6743\u65F6\u53D1\u751F\u9519\u8BEF: ${error.message}`);
    }
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/folder-select-modal.ts
var import_obsidian3 = require("obsidian");
var FolderSelectModal = class extends import_obsidian3.Modal {
  constructor(app, feishuApi, onSelect) {
    super(app);
    this.folders = [];
    this.currentPath = [];
    this.loading = false;
    this.feishuApi = feishuApi;
    this.onSelect = onSelect;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    contentEl.createEl("h2", { text: "\u9009\u62E9\u6587\u4EF6\u5939" });
    this.createBreadcrumb(contentEl);
    const listContainer = contentEl.createDiv("folder-list-container");
    listContainer.style.cssText = `
			max-height: 400px;
			overflow-y: auto;
			border: 1px solid var(--background-modifier-border);
			border-radius: 8px;
			margin: 16px 0;
		`;
    const buttonContainer = contentEl.createDiv("button-container");
    buttonContainer.style.cssText = `
			display: flex;
			justify-content: space-between;
			margin-top: 16px;
		`;
    const selectButton = buttonContainer.createEl("button", {
      text: "\u9009\u62E9\u5F53\u524D\u6587\u4EF6\u5939",
      cls: "mod-cta"
    });
    selectButton.onclick = () => {
      const currentFolder = this.currentPath.length > 0 ? this.currentPath[this.currentPath.length - 1] : null;
      this.onSelect(currentFolder);
      this.close();
    };
    const cancelButton = buttonContainer.createEl("button", {
      text: "\u53D6\u6D88"
    });
    cancelButton.onclick = () => {
      this.close();
    };
    this.loadFolders(listContainer);
  }
  /**
   * 创建面包屑导航
   */
  createBreadcrumb(containerEl) {
    const breadcrumbEl = containerEl.createDiv("folder-breadcrumb");
    breadcrumbEl.style.cssText = `
			display: flex;
			align-items: center;
			gap: 8px;
			margin: 16px 0;
			padding: 8px 12px;
			background: var(--background-secondary);
			border-radius: 6px;
			font-size: 14px;
		`;
    const rootEl = breadcrumbEl.createSpan("breadcrumb-item");
    rootEl.textContent = "\u6211\u7684\u7A7A\u95F4";
    rootEl.style.cssText = `
			cursor: pointer;
			color: var(--text-accent);
			text-decoration: underline;
		`;
    rootEl.onclick = () => this.navigateToRoot();
    this.currentPath.forEach((folder, index) => {
      breadcrumbEl.createSpan("breadcrumb-separator").textContent = " / ";
      const folderEl = breadcrumbEl.createSpan("breadcrumb-item");
      folderEl.textContent = folder.name;
      if (index < this.currentPath.length - 1) {
        folderEl.style.cssText = `
					cursor: pointer;
					color: var(--text-accent);
					text-decoration: underline;
				`;
        folderEl.onclick = () => this.navigateToFolder(index);
      } else {
        folderEl.style.cssText = `
					font-weight: bold;
					color: var(--text-normal);
				`;
      }
    });
  }
  /**
   * 加载文件夹列表
   */
  async loadFolders(containerEl) {
    if (this.loading)
      return;
    this.loading = true;
    containerEl.empty();
    const loadingEl = containerEl.createDiv("loading-indicator");
    loadingEl.textContent = "\u6B63\u5728\u52A0\u8F7D\u6587\u4EF6\u5939...";
    loadingEl.style.cssText = `
			text-align: center;
			padding: 20px;
			color: var(--text-muted);
		`;
    try {
      const parentFolderId = this.currentPath.length > 0 ? this.currentPath[this.currentPath.length - 1].folder_token || this.currentPath[this.currentPath.length - 1].token : void 0;
      const response = await this.feishuApi.getFolderList(parentFolderId);
      this.folders = response.data.folders;
      containerEl.empty();
      this.renderFolderList(containerEl);
    } catch (error) {
      console.error("Failed to load folders:", error);
      containerEl.empty();
      const errorEl = containerEl.createDiv("error-message");
      errorEl.textContent = `\u52A0\u8F7D\u5931\u8D25: ${error.message}`;
      errorEl.style.cssText = `
				text-align: center;
				padding: 20px;
				color: var(--text-error);
			`;
    } finally {
      this.loading = false;
    }
  }
  /**
   * 渲染文件夹列表
   */
  renderFolderList(containerEl) {
    if (this.folders.length === 0) {
      const emptyEl = containerEl.createDiv("empty-message");
      emptyEl.textContent = "\u6B64\u6587\u4EF6\u5939\u4E3A\u7A7A";
      emptyEl.style.cssText = `
				text-align: center;
				padding: 20px;
				color: var(--text-muted);
			`;
      return;
    }
    this.folders.forEach((folder) => {
      const folderEl = containerEl.createDiv("folder-item");
      folderEl.style.cssText = `
				display: flex;
				align-items: center;
				padding: 12px 16px;
				cursor: pointer;
				border-bottom: 1px solid var(--background-modifier-border);
				transition: background-color 0.2s;
			`;
      const iconEl = folderEl.createSpan("folder-icon");
      iconEl.textContent = "\u{1F4C1}";
      iconEl.style.cssText = `
				margin-right: 12px;
				font-size: 16px;
			`;
      const nameEl = folderEl.createSpan("folder-name");
      nameEl.textContent = folder.name;
      nameEl.style.cssText = `
				flex: 1;
				font-size: 14px;
			`;
      folderEl.onmouseenter = () => {
        folderEl.style.backgroundColor = "var(--background-modifier-hover)";
      };
      folderEl.onmouseleave = () => {
        folderEl.style.backgroundColor = "";
      };
      folderEl.onclick = () => {
        this.enterFolder(folder);
      };
    });
  }
  /**
   * 进入文件夹
   */
  async enterFolder(folder) {
    const existingIndex = this.currentPath.findIndex(
      (f) => (f.folder_token || f.token) === (folder.folder_token || folder.token)
    );
    if (existingIndex >= 0) {
      this.currentPath = this.currentPath.slice(0, existingIndex + 1);
    } else {
      this.currentPath.push(folder);
    }
    const breadcrumbEl = this.contentEl.querySelector(".folder-breadcrumb");
    if (breadcrumbEl) {
      breadcrumbEl.remove();
      this.createBreadcrumb(this.contentEl);
    }
    const listContainer = this.contentEl.querySelector(".folder-list-container");
    if (listContainer) {
      await this.loadFolders(listContainer);
    }
  }
  /**
   * 导航到根目录
   */
  async navigateToRoot() {
    this.currentPath = [];
    const breadcrumbEl = this.contentEl.querySelector(".folder-breadcrumb");
    if (breadcrumbEl) {
      breadcrumbEl.remove();
      this.createBreadcrumb(this.contentEl);
    }
    const listContainer = this.contentEl.querySelector(".folder-list-container");
    if (listContainer) {
      await this.loadFolders(listContainer);
    }
  }
  /**
   * 导航到指定层级的文件夹
   */
  async navigateToFolder(index) {
    this.currentPath = this.currentPath.slice(0, index + 1);
    const breadcrumbEl = this.contentEl.querySelector(".folder-breadcrumb");
    if (breadcrumbEl) {
      breadcrumbEl.remove();
      this.createBreadcrumb(this.contentEl);
    }
    const listContainer = this.contentEl.querySelector(".folder-list-container");
    if (listContainer) {
      await this.loadFolders(listContainer);
    }
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/settings.ts
var FeishuSettingTab = class extends import_obsidian4.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "\u98DE\u4E66\u5206\u4EAB\u8BBE\u7F6E" });
    const descEl = containerEl.createDiv("setting-item-description");
    descEl.innerHTML = `
			<p>\u76F4\u8FDE\u98DE\u4E66API\uFF0C\u56DE\u8C03\u5730\u5740\u4EC5\u4E2D\u8F6C\u65E0\u8BB0\u5F55\u3002</p>
			<p><strong>\u7279\u70B9\uFF1A</strong>\u65E0\u4F9D\u8D56\u3001\u66F4\u5B89\u5168\u3001\u54CD\u5E94\u66F4\u5FEB</p>
		`;
    containerEl.createEl("h3", { text: "\u{1F527} \u5E94\u7528\u914D\u7F6E" });
    new import_obsidian4.Setting(containerEl).setName("App ID").setDesc("\u98DE\u4E66\u5E94\u7528\u7684 App ID").addText((text) => text.setPlaceholder("\u8F93\u5165\u98DE\u4E66\u5E94\u7528\u7684 App ID").setValue(this.plugin.settings.appId).onChange(async (value) => {
      this.plugin.settings.appId = value.trim();
      await this.plugin.saveSettings();
    }));
    new import_obsidian4.Setting(containerEl).setName("App Secret").setDesc("\u98DE\u4E66\u5E94\u7528\u7684 App Secret").addText((text) => {
      text.setPlaceholder("\u8F93\u5165\u98DE\u4E66\u5E94\u7528\u7684 App Secret").setValue(this.plugin.settings.appSecret).onChange(async (value) => {
        this.plugin.settings.appSecret = value.trim();
        await this.plugin.saveSettings();
      });
      text.inputEl.type = "password";
    });
    new import_obsidian4.Setting(containerEl).setName("OAuth\u56DE\u8C03\u5730\u5740").setDesc("obsidian\u9700web\u56DE\u8C03\u4E2D\u8F6C\uFF0C\u4F8B\u5982\uFF1Ahttps://md2feishu.xinqi.life/oauth-callback").addText((text) => text.setPlaceholder("https://md2feishu.xinqi.life/oauth-callback").setValue(this.plugin.settings.callbackUrl).onChange(async (value) => {
      this.plugin.settings.callbackUrl = value.trim();
      await this.plugin.saveSettings();
    }));
    containerEl.createEl("h3", { text: "\u{1F510} \u6388\u6743\u7BA1\u7406" });
    const authStatusEl = containerEl.createDiv("setting-item");
    const authStatusInfo = authStatusEl.createDiv("setting-item-info");
    authStatusInfo.createDiv("setting-item-name").setText("\u6388\u6743\u72B6\u6001");
    const statusDesc = authStatusInfo.createDiv("setting-item-description");
    if (this.plugin.settings.userInfo) {
      statusDesc.innerHTML = `
				<span style="color: var(--text-success);">\u2705 \u5DF2\u6388\u6743</span><br>
				<strong>\u7528\u6237\uFF1A</strong>${this.plugin.settings.userInfo.name}<br>
				<strong>\u90AE\u7BB1\uFF1A</strong>${this.plugin.settings.userInfo.email}
			`;
    } else {
      statusDesc.innerHTML = '<span style="color: var(--text-error);">\u274C \u672A\u6388\u6743</span>';
    }
    new import_obsidian4.Setting(containerEl).setName("\u{1F680} \u4E00\u952E\u6388\u6743\uFF08\u63A8\u8350\uFF09").setDesc("\u81EA\u52A8\u6253\u5F00\u6D4F\u89C8\u5668\u5B8C\u6210\u6388\u6743\uFF0C\u901A\u8FC7\u4E91\u7AEF\u56DE\u8C03\u81EA\u52A8\u8FD4\u56DE\u6388\u6743\u7ED3\u679C\uFF0C\u65E0\u9700\u624B\u52A8\u64CD\u4F5C").addButton((button) => {
      button.setButtonText("\u{1F680} \u4E00\u952E\u6388\u6743").setCta().onClick(() => {
        this.startAutoAuth();
      });
    });
    new import_obsidian4.Setting(containerEl).setName("\u{1F4DD} \u624B\u52A8\u6388\u6743\uFF08\u5907\u7528\uFF09").setDesc("\u5982\u679C\u4E00\u952E\u6388\u6743\u9047\u5230\u95EE\u9898\uFF0C\u53EF\u4EE5\u4F7F\u7528\u4F20\u7EDF\u7684\u624B\u52A8\u590D\u5236\u7C98\u8D34\u6388\u6743\u65B9\u5F0F").addButton((button) => {
      button.setButtonText("\u624B\u52A8\u6388\u6743").onClick(() => {
        this.startManualAuth();
      });
    });
    if (this.plugin.settings.userInfo) {
      new import_obsidian4.Setting(containerEl).setName("\u6E05\u9664\u6388\u6743").setDesc("\u6E05\u9664\u5F53\u524D\u7684\u6388\u6743\u4FE1\u606F").addButton((button) => {
        button.setButtonText("\u{1F5D1}\uFE0F \u6E05\u9664\u6388\u6743").setWarning().onClick(async () => {
          this.plugin.settings.accessToken = "";
          this.plugin.settings.refreshToken = "";
          this.plugin.settings.userInfo = null;
          await this.plugin.saveSettings();
          this.plugin.feishuApi.updateSettings(this.plugin.settings);
          new import_obsidian4.Notice("\u2705 \u6388\u6743\u4FE1\u606F\u5DF2\u6E05\u9664");
          this.display();
        });
      });
    }
    if (this.plugin.settings.userInfo) {
      containerEl.createEl("h3", { text: "\u{1F4C1} \u9ED8\u8BA4\u6587\u4EF6\u5939" });
      new import_obsidian4.Setting(containerEl).setName("\u5F53\u524D\u9ED8\u8BA4\u6587\u4EF6\u5939").setDesc(`\u6587\u6863\u5C06\u4FDD\u5B58\u5230\uFF1A${this.plugin.settings.defaultFolderName || "\u6211\u7684\u7A7A\u95F4"}${this.plugin.settings.defaultFolderId ? ` (ID: ${this.plugin.settings.defaultFolderId})` : ""}`).addButton((button) => {
        button.setButtonText("\u{1F4C1} \u9009\u62E9\u6587\u4EF6\u5939").onClick(() => {
          this.showFolderSelectModal();
        });
      });
    }
    containerEl.createEl("h3", { text: "\u{1F4D6} \u4F7F\u7528\u8BF4\u660E" });
    const usageEl = containerEl.createDiv("setting-item-description");
    const usageLinkDiv = usageEl.createDiv("feishu-usage-link");
    usageLinkDiv.createEl("strong", { text: "\u{1F4DA} \u8BE6\u7EC6\u4F7F\u7528\u8BF4\u660E" });
    usageLinkDiv.createEl("br");
    const usageLink = usageLinkDiv.createEl("a", {
      text: "\u{1F517} \u70B9\u51FB\u67E5\u770B\u5B8C\u6574\u4F7F\u7528\u6559\u7A0B",
      href: "https://l0c34idk7v.feishu.cn/docx/Zk2VdWJPfoqmZhxPSJmcMfSbnHe"
    });
    usageLink.target = "_blank";
    const guideDiv = usageEl.createDiv("feishu-usage-guide");
    const guideTitle = guideDiv.createEl("strong", {
      text: "\u{1F4CB} \u5FEB\u901F\u914D\u7F6E\u6307\u5357",
      cls: "feishu-usage-guide-title"
    });
    const stepsList = guideDiv.createEl("ol");
    const step1 = stepsList.createEl("li");
    step1.createEl("strong", { text: "\u521B\u5EFA\u98DE\u4E66\u5E94\u7528\uFF1A" });
    step1.appendText("\u8BBF\u95EE ");
    const platformLink = step1.createEl("a", {
      text: "\u98DE\u4E66\u5F00\u653E\u5E73\u53F0 \u{1F517}",
      href: "https://open.feishu.cn/app"
    });
    platformLink.target = "_blank";
    step1.appendText(' \u521B\u5EFA"\u4F01\u4E1A\u81EA\u5EFA\u5E94\u7528"\uFF0C\u83B7\u53D6App ID\u548CApp Secret');
    const step2 = stepsList.createEl("li");
    step2.createEl("strong", { text: "\u914D\u7F6EOAuth\u56DE\u8C03\uFF1A" });
    step2.appendText('\u5728\u98DE\u4E66\u5E94\u7528"\u5B89\u5168\u8BBE\u7F6E"\u4E2D\u6DFB\u52A0\u56DE\u8C03\u5730\u5740\uFF1A');
    step2.createEl("br");
    step2.createEl("code", { text: "https://md2feishu.xinqi.life/oauth-callback" });
    step2.createEl("br");
    step2.createEl("span", {
      text: "\u{1F4A1} \u9ED8\u8BA4\u4F7F\u7528\u6211\u4EEC\u7684\u56DE\u8C03\u670D\u52A1\uFF0C\u4EE3\u7801\u5F00\u6E90\u53EF\u81EA\u884C\u90E8\u7F72",
      cls: "hint"
    });
    const step3 = stepsList.createEl("li");
    step3.createEl("strong", { text: "\u6DFB\u52A0\u5E94\u7528\u6743\u9650\uFF1A" });
    step3.appendText('\u5728"\u6743\u9650\u7BA1\u7406"\u4E2D\u6DFB\u52A0\u4EE5\u4E0B\u6743\u9650\uFF1A');
    const permList = step3.createEl("ul");
    permList.createEl("li", { text: "contact:user.base:readonly - \u83B7\u53D6\u7528\u6237\u57FA\u672C\u4FE1\u606F" });
    permList.createEl("li", { text: "docx:document - \u521B\u5EFA\u3001\u7F16\u8F91\u6587\u6863" });
    permList.createEl("li", { text: "drive:drive - \u8BBF\u95EE\u4E91\u7A7A\u95F4\u6587\u4EF6" });
    const step4 = stepsList.createEl("li");
    step4.createEl("strong", { text: "\u5B8C\u6210\u6388\u6743\uFF1A" });
    step4.appendText('\u5728\u4E0A\u65B9\u8F93\u5165App ID\u548CApp Secret\uFF0C\u70B9\u51FB"\u{1F680} \u4E00\u952E\u6388\u6743"');
    const step5 = stepsList.createEl("li");
    step5.createEl("strong", { text: "\u9009\u62E9\u6587\u4EF6\u5939\uFF1A" });
    step5.appendText("\u6388\u6743\u540E\u53EF\u9009\u62E9\u9ED8\u8BA4\u4FDD\u5B58\u6587\u4EF6\u5939\uFF08\u53EF\u9009\uFF09");
    const step6 = stepsList.createEl("li");
    step6.createEl("strong", { text: "\u5F00\u59CB\u4F7F\u7528\uFF1A" });
    step6.appendText('\u53F3\u952EMD\u6587\u4EF6\u9009\u62E9"\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66"\uFF0C\u6216\u4F7F\u7528\u547D\u4EE4\u9762\u677F');
    const featuresDiv = usageEl.createDiv("feishu-usage-guide");
    featuresDiv.createEl("strong", {
      text: "\u{1F389} \u529F\u80FD\u7279\u8272\uFF1A",
      cls: "feishu-usage-guide-title"
    });
    const featuresList = featuresDiv.createEl("ul");
    featuresList.createEl("li", { text: "\u2705 \u667A\u80FD\u6388\u6743\uFF1A\u81EA\u52A8\u68C0\u6D4Btoken\u72B6\u6001\uFF0C\u5931\u6548\u65F6\u81EA\u52A8\u91CD\u65B0\u6388\u6743" });
    featuresList.createEl("li", { text: "\u2705 \u65E0\u7F1D\u5206\u4EAB\uFF1A\u4E00\u952E\u5206\u4EAB\uFF0C\u81EA\u52A8\u5904\u7406\u6388\u6743\u548C\u8F6C\u6362\u6D41\u7A0B" });
    featuresList.createEl("li", { text: "\u2705 \u683C\u5F0F\u4FDD\u6301\uFF1A\u5B8C\u7F8E\u4FDD\u6301Markdown\u683C\u5F0F\uFF0C\u5305\u62EC\u56FE\u7247\u3001\u8868\u683C\u3001\u4EE3\u7801\u5757" });
    featuresList.createEl("li", { text: "\u2705 \u667A\u80FD\u5904\u7406\uFF1A\u81EA\u52A8\u5904\u7406Obsidian\u53CC\u5411\u94FE\u63A5\u3001\u6807\u7B7E\u7B49\u8BED\u6CD5" });
    featuresList.createEl("li", { text: "\u2705 \u53EF\u89C6\u5316\u9009\u62E9\uFF1A\u652F\u6301\u6D4F\u89C8\u548C\u9009\u62E9\u76EE\u6807\u6587\u4EF6\u5939" });
    featuresList.createEl("li", { text: "\u2705 \u4E00\u952E\u590D\u5236\uFF1A\u5206\u4EAB\u6210\u529F\u540E\u53EF\u4E00\u952E\u590D\u5236\u6587\u6863\u94FE\u63A5" });
    this.addAuthorSection(containerEl);
  }
  addAuthorSection(containerEl) {
    containerEl.createEl("hr", { cls: "feishu-author-separator" });
    const authorSection = containerEl.createDiv({ cls: "feishu-author-section" });
    authorSection.createEl("h4", {
      text: "\u{1F468}\u200D\u{1F4BB} \u4E86\u89E3\u4F5C\u8005",
      cls: "feishu-author-title"
    });
    authorSection.createEl("p", {
      text: "\u60F3\u4E86\u89E3\u66F4\u591A\u5173\u4E8E\u4F5C\u8005\u548C\u5176\u4ED6\u9879\u76EE\u7684\u4FE1\u606F\uFF1F",
      cls: "feishu-author-description"
    });
    const authorButton = authorSection.createEl("button", {
      text: "\u{1F310} \u8BBF\u95EE\u4F5C\u8005\u4E3B\u9875",
      cls: "feishu-author-button"
    });
    authorButton.addEventListener("click", () => {
      window.open("https://ai.xinqi.life/about", "_blank");
    });
  }
  startAutoAuth() {
    if (!this.plugin.settings.appId || !this.plugin.settings.appSecret) {
      new import_obsidian4.Notice("\u274C \u8BF7\u5148\u914D\u7F6E App ID \u548C App Secret");
      console.error("Missing App ID or App Secret");
      return;
    }
    this.plugin.feishuApi.updateSettings(this.plugin.settings);
    try {
      const authUrl = this.plugin.feishuApi.generateAuthUrl();
      window.open(authUrl, "_blank");
      new import_obsidian4.Notice("\u{1F504} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u8FD4\u56DEObsidian");
      const successHandler = () => {
        this.display();
        window.removeEventListener("feishu-auth-success", successHandler);
      };
      window.addEventListener("feishu-auth-success", successHandler);
    } catch (error) {
      console.error("Auto auth error:", error);
      new import_obsidian4.Notice(`\u274C \u81EA\u52A8\u6388\u6743\u5931\u8D25: ${error.message}`);
    }
  }
  startManualAuth() {
    if (!this.plugin.settings.appId || !this.plugin.settings.appSecret) {
      new import_obsidian4.Notice("\u274C \u8BF7\u5148\u914D\u7F6E App ID \u548C App Secret");
      console.error("Missing App ID or App Secret");
      return;
    }
    this.plugin.feishuApi.updateSettings(this.plugin.settings);
    const modal = new ManualAuthModal(
      this.app,
      this.plugin.feishuApi,
      async () => {
        await this.plugin.saveSettings();
        this.display();
      }
    );
    modal.open();
  }
  /**
   * 显示文件夹选择模态框
   */
  showFolderSelectModal() {
    const modal = new FolderSelectModal(
      this.app,
      this.plugin.feishuApi,
      async (selectedFolder) => {
        if (selectedFolder) {
          this.plugin.settings.defaultFolderId = selectedFolder.folder_token || selectedFolder.token || "";
          this.plugin.settings.defaultFolderName = selectedFolder.name;
        } else {
          console.log("\u{1F4C1} Root folder selected (\u6211\u7684\u7A7A\u95F4)");
          this.plugin.settings.defaultFolderId = "";
          this.plugin.settings.defaultFolderName = "\u6211\u7684\u7A7A\u95F4";
        }
        await this.plugin.saveSettings();
        new import_obsidian4.Notice("\u2705 \u9ED8\u8BA4\u6587\u4EF6\u5939\u8BBE\u7F6E\u5DF2\u4FDD\u5B58");
        this.display();
      }
    );
    modal.open();
  }
};

// src/main.ts
var FeishuSharePlugin = class extends import_obsidian5.Plugin {
  async onload() {
    await this.loadSettings();
    this.feishuApi = new FeishuApiService(this);
    this.registerObsidianProtocolHandler("feishu-auth", (params) => {
      this.handleOAuthCallback(params);
    });
    this.addSettingTab(new FeishuSettingTab(this.app, this));
    this.addCommand({
      id: "share-current-note",
      name: "\u5206\u4EAB\u5F53\u524D\u7B14\u8BB0\u5230\u98DE\u4E66",
      editorCallback: (editor, view) => {
        this.shareCurrentNote();
      }
    });
    this.registerEvent(
      this.app.workspace.on("file-menu", (menu, file) => {
        if (file instanceof import_obsidian5.TFile && file.extension === "md") {
          menu.addItem((item) => {
            item.setTitle("\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66").setIcon("share").onClick(() => {
              this.shareFile(file);
            });
          });
        }
      })
    );
    this.registerEvent(
      this.app.workspace.on("editor-menu", (menu, editor, view) => {
        menu.addItem((item) => {
          item.setTitle("\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66").setIcon("share").onClick(() => {
            this.shareCurrentNote();
          });
        });
      })
    );
  }
  onunload() {
  }
  async loadSettings() {
    const loadedData = await this.loadData();
    this.settings = Object.assign({}, DEFAULT_SETTINGS, loadedData);
  }
  async saveSettings() {
    await this.saveData(this.settings);
    if (this.feishuApi) {
      this.feishuApi.updateSettings(this.settings);
    }
  }
  /**
   * 处理OAuth回调
   */
  async handleOAuthCallback(params) {
    if (params.code) {
      new import_obsidian5.Notice("\u{1F504} \u6B63\u5728\u5904\u7406\u6388\u6743\u56DE\u8C03...");
      try {
        const success = await this.feishuApi.processCallback(`obsidian://feishu-auth?${new URLSearchParams(params).toString()}`);
        if (success) {
          new import_obsidian5.Notice("\u{1F389} \u81EA\u52A8\u6388\u6743\u6210\u529F\uFF01");
          await this.saveSettings();
          window.dispatchEvent(new CustomEvent("feishu-auth-success", {
            detail: {
              timestamp: Date.now(),
              source: "oauth-callback"
            }
          }));
        } else {
          new import_obsidian5.Notice("\u274C \u6388\u6743\u5904\u7406\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5");
        }
      } catch (error) {
        console.error("OAuth callback error:", error);
        new import_obsidian5.Notice(`\u274C \u6388\u6743\u5904\u7406\u5931\u8D25: ${error.message}`);
      }
    } else if (params.error) {
      new import_obsidian5.Notice(`\u274C \u6388\u6743\u5931\u8D25: ${params.error_description || params.error}`);
    } else {
      new import_obsidian5.Notice("\u274C \u65E0\u6548\u7684\u6388\u6743\u56DE\u8C03");
    }
  }
  /**
   * 分享当前笔记
   */
  async shareCurrentNote() {
    const activeFile = this.app.workspace.getActiveFile();
    if (!activeFile) {
      new import_obsidian5.Notice("\u274C \u6CA1\u6709\u6253\u5F00\u7684\u7B14\u8BB0");
      return;
    }
    if (activeFile.extension !== "md") {
      new import_obsidian5.Notice("\u274C \u53EA\u652F\u6301\u5206\u4EAB Markdown \u6587\u4EF6");
      return;
    }
    await this.shareFile(activeFile);
  }
  /**
   * 分享指定文件
   */
  async shareFile(file) {
    const statusNotice = new import_obsidian5.Notice("\u{1F504} \u6B63\u5728\u5206\u4EAB\u5230\u98DE\u4E66...", 0);
    try {
      if (!this.settings.accessToken || !this.settings.userInfo) {
        statusNotice.hide();
        new import_obsidian5.Notice("\u274C \u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u5B8C\u6210\u98DE\u4E66\u6388\u6743");
        return;
      }
      const rawContent = await this.app.vault.read(file);
      const title = file.basename;
      const result = await this.feishuApi.shareMarkdown(title, rawContent, statusNotice);
      statusNotice.hide();
      if (result.success) {
        if (result.url) {
          const linkNotice = new import_obsidian5.Notice("", 1e4);
          linkNotice.noticeEl.empty();
          linkNotice.noticeEl.addClass("feishu-notice-reset");
          const container = linkNotice.noticeEl.createDiv("feishu-success-container");
          const header = container.createDiv("feishu-success-header");
          const iconContainer = header.createDiv("feishu-icon-container");
          const icon = iconContainer.createEl("span", {
            text: "\u2713",
            cls: "feishu-success-icon"
          });
          const headerText = header.createDiv("feishu-header-text");
          const title2 = headerText.createEl("div", {
            text: "\u5206\u4EAB\u6210\u529F\uFF01",
            cls: "feishu-success-title"
          });
          const subtitle = headerText.createEl("div", {
            text: `\u6587\u6863\uFF1A${result.title}`,
            cls: "feishu-success-subtitle"
          });
          const buttonGroup = container.createDiv("feishu-button-group");
          const copyBtn = buttonGroup.createEl("button", {
            text: "\u{1F4CB} \u590D\u5236\u94FE\u63A5",
            cls: "feishu-copy-btn"
          });
          copyBtn.onclick = async () => {
            try {
              if (result.url) {
                await navigator.clipboard.writeText(result.url);
                copyBtn.textContent = "\u2705 \u5DF2\u590D\u5236";
                copyBtn.addClass("success");
                setTimeout(() => {
                  copyBtn.textContent = "\u{1F4CB} \u590D\u5236\u94FE\u63A5";
                  copyBtn.removeClass("success");
                }, 2e3);
              }
            } catch (error) {
              console.error("\u590D\u5236\u5931\u8D25:", error);
              copyBtn.textContent = "\u274C \u590D\u5236\u5931\u8D25";
              copyBtn.addClass("error");
              setTimeout(() => {
                copyBtn.textContent = "\u{1F4CB} \u590D\u5236\u94FE\u63A5";
                copyBtn.removeClass("error");
              }, 2e3);
            }
          };
          const openBtn = buttonGroup.createEl("button", {
            text: "\u{1F517} \u6253\u5F00",
            cls: "feishu-open-btn"
          });
          openBtn.onclick = () => {
            if (result.url) {
              window.open(result.url, "_blank");
            }
          };
          const closeBtn = container.createEl("button", {
            text: "\xD7",
            cls: "feishu-close-btn"
          });
          closeBtn.onclick = () => {
            linkNotice.hide();
          };
        } else {
          new import_obsidian5.Notice(`\u2705 \u5206\u4EAB\u6210\u529F\uFF01\u6587\u6863\u6807\u9898\uFF1A${result.title}`);
        }
      } else {
        new import_obsidian5.Notice(`\u274C \u5206\u4EAB\u5931\u8D25\uFF1A${result.error}`);
        console.error("Share failed:", result.error);
      }
    } catch (error) {
      statusNotice.hide();
      console.error("Share file error:", error);
      new import_obsidian5.Notice(`\u274C \u5206\u4EAB\u5931\u8D25\uFF1A${error.message}`);
    }
  }
  /**
   * 检查并刷新token
   */
  async ensureValidAuth() {
    if (!this.settings.accessToken) {
      return false;
    }
    return true;
  }
};
